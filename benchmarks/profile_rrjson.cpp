#include <iostream>
#include <chrono>
#include <vector>
#include <string>
#include <memory>
#include <fstream>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <numeric>
#include <functional>
#include <cstring>

// Profiling support
#ifdef ENABLE_PROFILING
#include <gperftools/profiler.h>
#endif

// Include rrjson
#include "../lib/rrjson.hpp"

/**
 * @brief Performance profiling tool for rrjson
 * 
 * This tool creates detailed performance profiles to identify bottlenecks
 * in rr<PERSON><PERSON>'s access patterns and parsing logic.
 */
class RRJsonProfiler {
public:
    struct ProfileResult {
        std::string operation_name;
        double total_time_ms;
        size_t iterations;
        double avg_time_per_op_ns;
        std::string profile_file;
    };

private:
    static constexpr int PROFILE_ITERATIONS = 10000;
    static constexpr int ACCESS_ITERATIONS = 50000;

public:
    auto run_profiling() -> void {
        std::cout << "🔍 rrjson Performance Profiling Tool\n";
        std::cout << "====================================\n\n";

        // Generate test data
        auto small_json = generate_test_json(100);
        auto medium_json = generate_test_json(1000);
        auto large_json = generate_test_json(5000);

        std::vector<ProfileResult> results;

        // Profile parsing
        results.push_back(profile_parsing("Small JSON Parsing", small_json));
        results.push_back(profile_parsing("Medium JSON Parsing", medium_json));
        results.push_back(profile_parsing("Large JSON Parsing", large_json));

        // Profile access patterns
        results.push_back(profile_access_patterns("Small JSON Access", small_json));
        results.push_back(profile_access_patterns("Medium JSON Access", medium_json));
        results.push_back(profile_access_patterns("Large JSON Access", large_json));

        // Profile specific operations
        results.push_back(profile_key_lookup("Key Lookup Operations", medium_json));
        results.push_back(profile_array_access("Array Access Operations", medium_json));
        results.push_back(profile_type_conversion("Type Conversion Operations", medium_json));

        print_profile_results(results);
        generate_profile_report(results);
    }

private:
    auto generate_test_json(size_t num_objects) -> std::string {
        std::ostringstream oss;
        oss << "{\n";
        oss << "  \"metadata\": {\n";
        oss << "    \"version\": \"1.0\",\n";
        oss << "    \"count\": " << num_objects << ",\n";
        oss << "    \"timestamp\": \"2024-06-13T10:30:00Z\"\n";
        oss << "  },\n";
        oss << "  \"data\": [\n";
        
        for (size_t i = 0; i < num_objects; ++i) {
            if (i > 0) oss << ",\n";
            oss << "    {\n";
            oss << "      \"id\": " << i << ",\n";
            oss << "      \"name\": \"Object " << i << "\",\n";
            oss << "      \"value\": " << (i * 3.14159) << ",\n";
            oss << "      \"active\": " << (i % 2 == 0 ? "true" : "false") << ",\n";
            oss << "      \"tags\": [\"tag" << i << "\", \"category" << (i % 5) << "\"],\n";
            oss << "      \"nested\": {\n";
            oss << "        \"level\": " << (i % 10) << ",\n";
            oss << "        \"score\": " << (i * 0.1 + 1.0) << "\n";
            oss << "      }\n";
            oss << "    }";
        }
        
        oss << "\n  ]\n";
        oss << "}";
        
        return oss.str();
    }

    auto measure_time_ns(std::function<void()> func) -> double {
        auto start = std::chrono::high_resolution_clock::now();
        func();
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(end - start);
        return static_cast<double>(duration.count());
    }

    auto profile_parsing(const std::string& name, const std::string& json_data) -> ProfileResult {
        std::cout << "Profiling: " << name << "...\n";
        
        ProfileResult result;
        result.operation_name = name;
        result.iterations = PROFILE_ITERATIONS;
        result.profile_file = "profile_" + std::string(name.begin(), name.end()) + ".prof";
        
        // Replace spaces with underscores for filename
        std::replace(result.profile_file.begin(), result.profile_file.end(), ' ', '_');

#ifdef ENABLE_PROFILING
        ProfilerStart(result.profile_file.c_str());
#endif

        std::vector<double> times;
        for (int i = 0; i < PROFILE_ITERATIONS; ++i) {
            std::string json_copy = json_data;
            auto time = measure_time_ns([&]() {
                rrjson::Element root(std::move(json_copy));
                volatile auto type = root.type();
                (void)type;
            });
            times.push_back(time);
        }

#ifdef ENABLE_PROFILING
        ProfilerStop();
#endif

        result.total_time_ms = std::accumulate(times.begin(), times.end(), 0.0) / 1000000.0;
        result.avg_time_per_op_ns = result.total_time_ms * 1000000.0 / result.iterations;
        
        return result;
    }

    auto profile_access_patterns(const std::string& name, const std::string& json_data) -> ProfileResult {
        std::cout << "Profiling: " << name << "...\n";
        
        ProfileResult result;
        result.operation_name = name;
        result.iterations = ACCESS_ITERATIONS;
        result.profile_file = "profile_" + std::string(name.begin(), name.end()) + ".prof";
        
        // Replace spaces with underscores for filename
        std::replace(result.profile_file.begin(), result.profile_file.end(), ' ', '_');

        std::string json_copy = json_data;
        rrjson::Element root(std::move(json_copy));

#ifdef ENABLE_PROFILING
        ProfilerStart(result.profile_file.c_str());
#endif

        std::vector<double> times;
        for (int i = 0; i < ACCESS_ITERATIONS; ++i) {
            auto time = measure_time_ns([&]() {
                try {
                    auto metadata = root["metadata"];
                    auto version = metadata["version"].as_string();
                    auto data_array = root["data"];
                    if (data_array.size() > 0) {
                        size_t idx = i % std::min(size_t(100), data_array.size());
                        auto obj = data_array[idx];
                        auto id = obj["id"].as_int();
                        auto name = obj["name"].as_string();
                        
                        // Use values to prevent optimization
                        volatile auto v1 = version.size();
                        volatile auto v2 = id;
                        volatile auto v3 = name.size();
                        (void)v1; (void)v2; (void)v3;
                    }
                } catch (...) {
                    // Ignore errors for profiling
                }
            });
            times.push_back(time);
        }

#ifdef ENABLE_PROFILING
        ProfilerStop();
#endif

        result.total_time_ms = std::accumulate(times.begin(), times.end(), 0.0) / 1000000.0;
        result.avg_time_per_op_ns = result.total_time_ms * 1000000.0 / result.iterations;
        
        return result;
    }

    auto profile_key_lookup(const std::string& name, const std::string& json_data) -> ProfileResult {
        std::cout << "Profiling: " << name << "...\n";
        
        ProfileResult result;
        result.operation_name = name;
        result.iterations = ACCESS_ITERATIONS;
        result.profile_file = "profile_Key_Lookup_Operations.prof";

        std::string json_copy = json_data;
        rrjson::Element root(std::move(json_copy));

#ifdef ENABLE_PROFILING
        ProfilerStart(result.profile_file.c_str());
#endif

        std::vector<double> times;
        for (int i = 0; i < ACCESS_ITERATIONS; ++i) {
            auto time = measure_time_ns([&]() {
                try {
                    // Test key lookup performance
                    auto metadata = root["metadata"];
                    auto data = root["data"];
                    auto nonexistent = root["nonexistent_key"];
                } catch (const rrjson::key_error&) {
                    // Expected for nonexistent key
                }
            });
            times.push_back(time);
        }

#ifdef ENABLE_PROFILING
        ProfilerStop();
#endif

        result.total_time_ms = std::accumulate(times.begin(), times.end(), 0.0) / 1000000.0;
        result.avg_time_per_op_ns = result.total_time_ms * 1000000.0 / result.iterations;
        
        return result;
    }

    auto profile_array_access(const std::string& name, const std::string& json_data) -> ProfileResult {
        std::cout << "Profiling: " << name << "...\n";
        
        ProfileResult result;
        result.operation_name = name;
        result.iterations = ACCESS_ITERATIONS;
        result.profile_file = "profile_Array_Access_Operations.prof";

        std::string json_copy = json_data;
        rrjson::Element root(std::move(json_copy));

#ifdef ENABLE_PROFILING
        ProfilerStart(result.profile_file.c_str());
#endif

        std::vector<double> times;
        for (int i = 0; i < ACCESS_ITERATIONS; ++i) {
            auto time = measure_time_ns([&]() {
                try {
                    auto data_array = root["data"];
                    if (data_array.size() > 0) {
                        size_t idx = i % std::min(size_t(100), data_array.size());
                        auto obj = data_array[idx];
                        volatile auto ptr = &obj;
                        (void)ptr;
                    }
                } catch (...) {
                    // Ignore errors
                }
            });
            times.push_back(time);
        }

#ifdef ENABLE_PROFILING
        ProfilerStop();
#endif

        result.total_time_ms = std::accumulate(times.begin(), times.end(), 0.0) / 1000000.0;
        result.avg_time_per_op_ns = result.total_time_ms * 1000000.0 / result.iterations;
        
        return result;
    }

    auto profile_type_conversion(const std::string& name, const std::string& json_data) -> ProfileResult {
        std::cout << "Profiling: " << name << "...\n";
        
        ProfileResult result;
        result.operation_name = name;
        result.iterations = ACCESS_ITERATIONS;
        result.profile_file = "profile_Type_Conversion_Operations.prof";

        std::string json_copy = json_data;
        rrjson::Element root(std::move(json_copy));

#ifdef ENABLE_PROFILING
        ProfilerStart(result.profile_file.c_str());
#endif

        std::vector<double> times;
        for (int i = 0; i < ACCESS_ITERATIONS; ++i) {
            auto time = measure_time_ns([&]() {
                try {
                    auto data_array = root["data"];
                    if (data_array.size() > 0) {
                        auto obj = data_array[0];
                        auto id = obj["id"].as_int();
                        auto name = obj["name"].as_string();
                        auto value = obj["value"].as_number();
                        auto active = obj["active"].as_bool();
                        
                        volatile auto v1 = id;
                        volatile auto v2 = name.size();
                        volatile auto v3 = value;
                        volatile auto v4 = active;
                        (void)v1; (void)v2; (void)v3; (void)v4;
                    }
                } catch (...) {
                    // Ignore errors
                }
            });
            times.push_back(time);
        }

#ifdef ENABLE_PROFILING
        ProfilerStop();
#endif

        result.total_time_ms = std::accumulate(times.begin(), times.end(), 0.0) / 1000000.0;
        result.avg_time_per_op_ns = result.total_time_ms * 1000000.0 / result.iterations;

        return result;
    }

    auto print_profile_results(const std::vector<ProfileResult>& results) -> void {
        std::cout << "\n🔍 Performance Profile Results\n";
        std::cout << "==============================\n\n";

        std::cout << std::fixed << std::setprecision(3);
        std::cout << std::left << std::setw(30) << "Operation"
                  << std::setw(15) << "Total (ms)"
                  << std::setw(15) << "Iterations"
                  << std::setw(15) << "Avg (ns/op)"
                  << std::setw(25) << "Profile File" << "\n";
        std::cout << std::string(100, '-') << "\n";

        for (const auto& result : results) {
            std::cout << std::left << std::setw(30) << result.operation_name
                      << std::setw(15) << result.total_time_ms
                      << std::setw(15) << result.iterations
                      << std::setw(15) << result.avg_time_per_op_ns
                      << std::setw(25) << result.profile_file << "\n";
        }
        std::cout << "\n";
    }

    auto generate_profile_report(const std::vector<ProfileResult>& results) -> void {
        std::ofstream report("benchmarks/PROFILE_RESULTS.md");
        if (!report.is_open()) {
            std::cerr << "Failed to create profile report file\n";
            return;
        }

        report << "# rrjson Performance Profile Results\n\n";
        report << "Generated on: " << std::chrono::system_clock::now().time_since_epoch().count() << "\n\n";

        report << "## Summary\n\n";
        report << "This report contains detailed performance profiling results for rrjson operations.\n";
        report << "Each operation was profiled using Google's gperftools profiler to identify bottlenecks.\n\n";

        report << "## Results Table\n\n";
        report << "| Operation | Total Time (ms) | Iterations | Avg Time (ns/op) | Profile File |\n";
        report << "|-----------|-----------------|------------|------------------|-------------|\n";

        for (const auto& result : results) {
            report << "| " << result.operation_name
                   << " | " << std::fixed << std::setprecision(3) << result.total_time_ms
                   << " | " << result.iterations
                   << " | " << std::fixed << std::setprecision(1) << result.avg_time_per_op_ns
                   << " | " << result.profile_file << " |\n";
        }

        report << "\n## Analysis\n\n";

        // Find slowest operations
        auto slowest = std::max_element(results.begin(), results.end(),
            [](const ProfileResult& a, const ProfileResult& b) {
                return a.avg_time_per_op_ns < b.avg_time_per_op_ns;
            });

        if (slowest != results.end()) {
            report << "### Slowest Operation\n";
            report << "- **" << slowest->operation_name << "**: "
                   << std::fixed << std::setprecision(1) << slowest->avg_time_per_op_ns << " ns/op\n\n";
        }

        // Categorize results
        std::vector<ProfileResult> parsing_results;
        std::vector<ProfileResult> access_results;
        std::vector<ProfileResult> operation_results;

        for (const auto& result : results) {
            if (result.operation_name.find("Parsing") != std::string::npos) {
                parsing_results.push_back(result);
            } else if (result.operation_name.find("Access") != std::string::npos) {
                access_results.push_back(result);
            } else {
                operation_results.push_back(result);
            }
        }

        if (!parsing_results.empty()) {
            report << "### Parsing Performance\n";
            for (const auto& result : parsing_results) {
                report << "- " << result.operation_name << ": "
                       << std::fixed << std::setprecision(1) << result.avg_time_per_op_ns << " ns/op\n";
            }
            report << "\n";
        }

        if (!access_results.empty()) {
            report << "### Access Performance\n";
            for (const auto& result : access_results) {
                report << "- " << result.operation_name << ": "
                       << std::fixed << std::setprecision(1) << result.avg_time_per_op_ns << " ns/op\n";
            }
            report << "\n";
        }

        if (!operation_results.empty()) {
            report << "### Specific Operations\n";
            for (const auto& result : operation_results) {
                report << "- " << result.operation_name << ": "
                       << std::fixed << std::setprecision(1) << result.avg_time_per_op_ns << " ns/op\n";
            }
            report << "\n";
        }

        report << "## How to Analyze Profile Files\n\n";
        report << "To analyze the generated profile files, use:\n\n";
        report << "```bash\n";
        report << "# Install pprof if not already installed\n";
        report << "go install github.com/google/pprof@latest\n\n";
        report << "# Analyze a profile file\n";
        report << "pprof --text ./profile_rrjson profile_Small_JSON_Access.prof\n";
        report << "pprof --web ./profile_rrjson profile_Small_JSON_Access.prof\n";
        report << "```\n\n";

        report << "## Recommendations\n\n";
        report << "Based on the profiling results:\n\n";

        // Generate recommendations based on results
        if (slowest != results.end()) {
            if (slowest->operation_name.find("Key Lookup") != std::string::npos) {
                report << "1. **Key Lookup Optimization**: Consider adding a `contains()` method to avoid exception overhead\n";
            }
            if (slowest->operation_name.find("Access") != std::string::npos) {
                report << "2. **Access Pattern Optimization**: Reduce redundant key lookups and exception handling\n";
            }
            if (slowest->operation_name.find("Type Conversion") != std::string::npos) {
                report << "3. **Type Conversion Optimization**: Optimize template instantiation and type checking\n";
            }
        }

        report << "\n## Profile Files Generated\n\n";
        for (const auto& result : results) {
            report << "- `" << result.profile_file << "` - " << result.operation_name << "\n";
        }

        report.close();
        std::cout << "📊 Profile report saved to: benchmarks/PROFILE_RESULTS.md\n";
    }
};

int main() {
    std::cout << "🚀 Starting rrjson Performance Profiling...\n\n";

#ifdef ENABLE_PROFILING
    std::cout << "✅ Profiling enabled (gperftools)\n";
#else
    std::cout << "⚠️  Profiling disabled. Compile with -DENABLE_PROFILING and link with -lprofiler\n";
#endif

    RRJsonProfiler profiler;
    profiler.run_profiling();

    std::cout << "\n✅ Profiling completed!\n\n";

    std::cout << "📁 Generated files:\n";
    std::cout << "   • benchmarks/PROFILE_RESULTS.md - Detailed analysis report\n";
    std::cout << "   • profile_*.prof - Raw profile data files\n\n";

    std::cout << "🔧 To analyze profile files:\n";
    std::cout << "   pprof --text ./profile_rrjson profile_Small_JSON_Access.prof\n";
    std::cout << "   pprof --web ./profile_rrjson profile_Small_JSON_Access.prof\n\n";

    std::cout << "💡 For best results, compile with:\n";
    std::cout << "   g++ -std=c++23 -O2 -g -DENABLE_PROFILING profile_rrjson.cpp -lprofiler -o profile_rrjson\n";

    return 0;
}
