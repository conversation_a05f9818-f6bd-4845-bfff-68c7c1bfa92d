#include <iostream>
#include <chrono>
#include <vector>
#include <string>
#include <memory>
#include <fstream>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <numeric>
#include <functional>
#include <cmath>

// Include both versions
#include "../lib/rrjson.hpp"
#include "../lib/rrjson_v2.hpp"

/**
 * @brief Performance comparison between rrjson v1 and v2
 * 
 * Tests the effectiveness of the major redesign in addressing
 * the identified performance bottlenecks.
 */
class V2PerformanceTest {
private:
    static constexpr int ITERATIONS = 10000;
    static constexpr int ACCESS_ITERATIONS = 100000;

public:
    struct BenchmarkResult {
        std::string test_name;
        double v1_time_ns;
        double v2_time_ns;
        double improvement_ratio;
        size_t iterations;
    };

    auto run_comparison() -> void {
        std::cout << "🚀 rrjson v2 Performance Comparison\n";
        std::cout << "===================================\n\n";

        // Generate test data
        auto small_json = generate_test_json(100);
        auto medium_json = generate_test_json(1000);
        auto large_json = generate_test_json(5000);

        std::vector<BenchmarkResult> results;

        // Test parsing performance
        results.push_back(compare_parsing("Small JSON Parsing", small_json, ITERATIONS));
        results.push_back(compare_parsing("Medium JSON Parsing", medium_json, ITERATIONS));
        results.push_back(compare_parsing("Large JSON Parsing", large_json, ITERATIONS / 2));

        // Test access performance
        results.push_back(compare_access("Key Access (existing)", medium_json, ACCESS_ITERATIONS));
        results.push_back(compare_access("Key Access (contains)", medium_json, ACCESS_ITERATIONS));
        results.push_back(compare_number_conversion("Number Conversion", medium_json, ACCESS_ITERATIONS));
        results.push_back(compare_string_access("String Access", medium_json, ACCESS_ITERATIONS));

        print_results(results);
        generate_report(results);
    }

private:
    auto generate_test_json(size_t num_objects) -> std::string {
        std::ostringstream oss;
        oss << "{\n";
        oss << "  \"metadata\": {\n";
        oss << "    \"version\": \"2.0\",\n";
        oss << "    \"count\": " << num_objects << "\n";
        oss << "  },\n";
        oss << "  \"data\": [\n";
        
        for (size_t i = 0; i < num_objects; ++i) {
            if (i > 0) oss << ",\n";
            oss << "    {\n";
            oss << "      \"id\": " << i << ",\n";
            oss << "      \"name\": \"Object " << i << "\",\n";
            oss << "      \"value\": " << (i * 3.14159) << ",\n";
            oss << "      \"active\": " << (i % 2 == 0 ? "true" : "false") << "\n";
            oss << "    }";
        }
        
        oss << "\n  ]\n";
        oss << "}";
        
        return oss.str();
    }

    auto measure_time_ns(std::function<void()> func) -> double {
        auto start = std::chrono::high_resolution_clock::now();
        func();
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(end - start);
        return static_cast<double>(duration.count());
    }

    auto compare_parsing(const std::string& test_name, const std::string& json_data, int iterations) -> BenchmarkResult {
        std::cout << "Testing: " << test_name << "...\n";

        // Test v1
        std::vector<double> v1_times;
        for (int i = 0; i < iterations; ++i) {
            std::string json_copy = json_data;
            auto time = measure_time_ns([&]() {
                rrjson::Element root(std::move(json_copy));
                volatile auto type = root.is_object();
                (void)type;
            });
            v1_times.push_back(time);
        }

        // Test v2
        std::vector<double> v2_times;
        for (int i = 0; i < iterations; ++i) {
            std::string json_copy = json_data;
            auto time = measure_time_ns([&]() {
                rrjson::v2::Element root(std::move(json_copy));
                volatile auto type = root.is_object();
                (void)type;
            });
            v2_times.push_back(time);
        }

        double v1_avg = std::accumulate(v1_times.begin(), v1_times.end(), 0.0) / iterations;
        double v2_avg = std::accumulate(v2_times.begin(), v2_times.end(), 0.0) / iterations;

        return {test_name, v1_avg, v2_avg, v1_avg / v2_avg, static_cast<size_t>(iterations)};
    }

    auto compare_access(const std::string& test_name, const std::string& json_data, int iterations) -> BenchmarkResult {
        std::cout << "Testing: " << test_name << "...\n";

        // Pre-parse for both versions
        std::string json_copy1 = json_data;
        std::string json_copy2 = json_data;
        rrjson::Element root_v1(std::move(json_copy1));
        rrjson::v2::Element root_v2(std::move(json_copy2));

        // Test v1 access
        std::vector<double> v1_times;
        for (int i = 0; i < iterations; ++i) {
            auto time = measure_time_ns([&]() {
                try {
                    auto metadata = root_v1["metadata"];
                    auto version = metadata["version"];
                    volatile auto ptr = &version;
                    (void)ptr;
                } catch (...) {
                    // Ignore errors
                }
            });
            v1_times.push_back(time);
        }

        // Test v2 access with contains()
        std::vector<double> v2_times;
        for (int i = 0; i < iterations; ++i) {
            auto time = measure_time_ns([&]() {
                if (root_v2.contains("metadata")) {
                    auto& metadata = root_v2["metadata"];
                    if (metadata.contains("version")) {
                        auto& version = metadata["version"];
                        volatile auto ptr = &version;
                        (void)ptr;
                    }
                }
            });
            v2_times.push_back(time);
        }

        double v1_avg = std::accumulate(v1_times.begin(), v1_times.end(), 0.0) / iterations;
        double v2_avg = std::accumulate(v2_times.begin(), v2_times.end(), 0.0) / iterations;

        return {test_name, v1_avg, v2_avg, v1_avg / v2_avg, static_cast<size_t>(iterations)};
    }

    auto compare_number_conversion(const std::string& test_name, const std::string& json_data, int iterations) -> BenchmarkResult {
        std::cout << "Testing: " << test_name << "...\n";

        // Pre-parse for both versions
        std::string json_copy1 = json_data;
        std::string json_copy2 = json_data;
        rrjson::Element root_v1(std::move(json_copy1));
        rrjson::v2::Element root_v2(std::move(json_copy2));

        // Test v1 number conversion
        std::vector<double> v1_times;
        for (int i = 0; i < iterations; ++i) {
            auto time = measure_time_ns([&]() {
                try {
                    auto data = root_v1["data"];
                    if (data.size() > 0) {
                        auto first = data[0];
                        auto id = first["id"].as_int();
                        auto value = first["value"].as_number();
                        volatile auto v1 = id;
                        volatile auto v2 = value;
                        (void)v1; (void)v2;
                    }
                } catch (...) {
                    // Ignore errors
                }
            });
            v1_times.push_back(time);
        }

        // Test v2 number conversion
        std::vector<double> v2_times;
        for (int i = 0; i < iterations; ++i) {
            auto time = measure_time_ns([&]() {
                if (root_v2.contains("data")) {
                    auto& data = root_v2["data"];
                    if (data.size() > 0) {
                        auto& first = data[0];
                        auto id = first["id"].as_int();
                        auto value = first["value"].as_number();
                        volatile auto v1 = id;
                        volatile auto v2 = value;
                        (void)v1; (void)v2;
                    }
                }
            });
            v2_times.push_back(time);
        }

        double v1_avg = std::accumulate(v1_times.begin(), v1_times.end(), 0.0) / iterations;
        double v2_avg = std::accumulate(v2_times.begin(), v2_times.end(), 0.0) / iterations;

        return {test_name, v1_avg, v2_avg, v1_avg / v2_avg, static_cast<size_t>(iterations)};
    }

    auto compare_string_access(const std::string& test_name, const std::string& json_data, int iterations) -> BenchmarkResult {
        std::cout << "Testing: " << test_name << "...\n";

        // Pre-parse for both versions
        std::string json_copy1 = json_data;
        std::string json_copy2 = json_data;
        rrjson::Element root_v1(std::move(json_copy1));
        rrjson::v2::Element root_v2(std::move(json_copy2));

        // Test v1 string access
        std::vector<double> v1_times;
        for (int i = 0; i < iterations; ++i) {
            auto time = measure_time_ns([&]() {
                try {
                    auto metadata = root_v1["metadata"];
                    auto version = metadata["version"].as_string();
                    volatile auto len = version.size();
                    (void)len;
                } catch (...) {
                    // Ignore errors
                }
            });
            v1_times.push_back(time);
        }

        // Test v2 string access
        std::vector<double> v2_times;
        for (int i = 0; i < iterations; ++i) {
            auto time = measure_time_ns([&]() {
                if (root_v2.contains("metadata")) {
                    auto& metadata = root_v2["metadata"];
                    if (metadata.contains("version")) {
                        auto version = metadata["version"].as_string();
                        volatile auto len = version.size();
                        (void)len;
                    }
                }
            });
            v2_times.push_back(time);
        }

        double v1_avg = std::accumulate(v1_times.begin(), v1_times.end(), 0.0) / iterations;
        double v2_avg = std::accumulate(v2_times.begin(), v2_times.end(), 0.0) / iterations;

        return {test_name, v1_avg, v2_avg, v1_avg / v2_avg, static_cast<size_t>(iterations)};
    }

    auto print_results(const std::vector<BenchmarkResult>& results) -> void {
        std::cout << "\n🚀 Performance Comparison Results\n";
        std::cout << "=================================\n\n";

        std::cout << std::fixed << std::setprecision(1);
        std::cout << std::left << std::setw(25) << "Test"
                  << std::setw(15) << "v1 (ns)"
                  << std::setw(15) << "v2 (ns)"
                  << std::setw(15) << "Improvement"
                  << std::setw(12) << "Iterations" << "\n";
        std::cout << std::string(82, '-') << "\n";

        double total_improvement = 1.0;
        for (const auto& result : results) {
            std::cout << std::left << std::setw(25) << result.test_name
                      << std::setw(15) << result.v1_time_ns
                      << std::setw(15) << result.v2_time_ns
                      << std::setw(15) << (std::to_string(static_cast<int>(result.improvement_ratio)) + "x")
                      << std::setw(12) << result.iterations << "\n";
            total_improvement *= result.improvement_ratio;
        }

        std::cout << "\n";
        std::cout << "📊 Overall geometric mean improvement: "
                  << std::fixed << std::setprecision(1)
                  << std::pow(total_improvement, 1.0 / results.size()) << "x\n\n";

        // Find best improvements
        auto best_improvement = std::max_element(results.begin(), results.end(),
            [](const BenchmarkResult& a, const BenchmarkResult& b) {
                return a.improvement_ratio < b.improvement_ratio;
            });

        if (best_improvement != results.end()) {
            std::cout << "🏆 Best improvement: " << best_improvement->test_name
                      << " (" << std::fixed << std::setprecision(1)
                      << best_improvement->improvement_ratio << "x faster)\n";
        }
    }

    auto generate_report(const std::vector<BenchmarkResult>& results) -> void {
        std::ofstream report("benchmarks/V2_PERFORMANCE_REPORT.md");
        if (!report.is_open()) {
            std::cerr << "Failed to create v2 performance report\n";
            return;
        }

        report << "# rrjson v2 Performance Report\n\n";
        report << "## Executive Summary\n\n";
        report << "This report compares the performance of rrjson v2 against v1, measuring the effectiveness\n";
        report << "of the major architectural redesign in addressing identified bottlenecks.\n\n";

        report << "## Key Improvements in v2\n\n";
        report << "1. **FastNumberParser**: Replaced `strtof64` (15.4% CPU bottleneck)\n";
        report << "2. **FastObjectMap**: Replaced `std::unordered_map` (6.8% CPU bottleneck)\n";
        report << "3. **FastWhitespaceSkipper**: SIMD-optimized whitespace processing (8.5% CPU bottleneck)\n";
        report << "4. **contains() methods**: Eliminated exception overhead for key existence checks\n";
        report << "5. **Cache-friendly design**: Improved memory access patterns\n\n";

        report << "## Performance Results\n\n";
        report << "| Test | v1 (ns) | v2 (ns) | Improvement | Iterations |\n";
        report << "|------|---------|---------|-------------|------------|\n";

        double total_improvement = 1.0;
        for (const auto& result : results) {
            report << "| " << result.test_name
                   << " | " << std::fixed << std::setprecision(1) << result.v1_time_ns
                   << " | " << std::fixed << std::setprecision(1) << result.v2_time_ns
                   << " | " << std::fixed << std::setprecision(1) << result.improvement_ratio << "x"
                   << " | " << result.iterations << " |\n";
            total_improvement *= result.improvement_ratio;
        }

        double geometric_mean = std::pow(total_improvement, 1.0 / results.size());
        report << "\n**Overall Improvement**: " << std::fixed << std::setprecision(1)
               << geometric_mean << "x faster\n\n";

        report.close();
        std::cout << "📊 V2 performance report saved to: benchmarks/V2_PERFORMANCE_REPORT.md\n";
    }
};

int main() {
    std::cout << "🚀 Starting rrjson v2 Performance Comparison...\n\n";

    V2PerformanceTest tester;
    tester.run_comparison();

    std::cout << "\n✅ Performance comparison completed!\n\n";

    return 0;
}
