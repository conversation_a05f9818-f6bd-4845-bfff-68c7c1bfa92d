#include <iostream>
#include <chrono>
#include <vector>
#include <string>
#include <memory>
#include <fstream>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <numeric>
#include <functional>
#include <map>

// Include rrjson
#include "../lib/rrjson.hpp"

/**
 * @brief Simple performance profiler for rrjson without external dependencies
 * 
 * This tool measures execution time of different operations to identify bottlenecks.
 */
class SimpleProfiler {
public:
    struct OperationStats {
        std::string name;
        std::vector<double> times_ns;
        double total_time_ms;
        double avg_time_ns;
        double min_time_ns;
        double max_time_ns;
        double std_dev_ns;
        size_t iterations;
    };

private:
    static constexpr int ITERATIONS = 10000;
    static constexpr int ACCESS_ITERATIONS = 50000;

public:
    auto run_profiling() -> void {
        std::cout << "🔍 Simple rrjson Performance Profiler\n";
        std::cout << "=====================================\n\n";

        // Generate test data
        auto small_json = generate_test_json(100);
        auto medium_json = generate_test_json(1000);

        std::vector<OperationStats> stats;

        // Profile individual operations
        stats.push_back(profile_operation("JSON Parsing (Small)", [&]() {
            std::string json_copy = small_json;
            rrjson::Element root(std::move(json_copy));
            volatile auto type = root.type();
            (void)type;
        }, ITERATIONS));

        stats.push_back(profile_operation("JSON Parsing (Medium)", [&]() {
            std::string json_copy = medium_json;
            rrjson::Element root(std::move(json_copy));
            volatile auto type = root.type();
            (void)type;
        }, ITERATIONS));

        // Profile access operations with pre-parsed JSON
        std::string medium_copy = medium_json;
        rrjson::Element root(std::move(medium_copy));

        stats.push_back(profile_operation("Key Access (existing)", [&]() {
            auto metadata = root["metadata"];
            volatile auto ptr = &metadata;
            (void)ptr;
        }, ACCESS_ITERATIONS));

        stats.push_back(profile_operation("Key Access (non-existing)", [&]() {
            try {
                auto nonexistent = root["nonexistent_key"];
                volatile auto ptr = &nonexistent;
                (void)ptr;
            } catch (const rrjson::key_error&) {
                // Expected
            }
        }, ACCESS_ITERATIONS));

        stats.push_back(profile_operation("Array Index Access", [&]() {
            try {
                auto data = root["data"];
                auto first = data[0];
                volatile auto ptr = &first;
                (void)ptr;
            } catch (...) {
                // Ignore
            }
        }, ACCESS_ITERATIONS));

        stats.push_back(profile_operation("String Conversion", [&]() {
            try {
                auto metadata = root["metadata"];
                auto version = metadata["version"].as_string();
                volatile auto len = version.size();
                (void)len;
            } catch (...) {
                // Ignore
            }
        }, ACCESS_ITERATIONS));

        stats.push_back(profile_operation("Integer Conversion", [&]() {
            try {
                auto data = root["data"];
                auto first = data[0];
                auto id = first["id"].as_int();
                volatile auto val = id;
                (void)val;
            } catch (...) {
                // Ignore
            }
        }, ACCESS_ITERATIONS));

        stats.push_back(profile_operation("Number Conversion", [&]() {
            try {
                auto data = root["data"];
                auto first = data[0];
                auto value = first["value"].as_number();
                volatile auto val = value;
                (void)val;
            } catch (...) {
                // Ignore
            }
        }, ACCESS_ITERATIONS));

        stats.push_back(profile_operation("Boolean Conversion", [&]() {
            try {
                auto data = root["data"];
                auto first = data[0];
                auto active = first["active"].as_bool();
                volatile auto val = active;
                (void)val;
            } catch (...) {
                // Ignore
            }
        }, ACCESS_ITERATIONS));

        stats.push_back(profile_operation("Complex Access Pattern", [&]() {
            try {
                auto metadata = root["metadata"];
                auto version = metadata["version"].as_string();
                auto data = root["data"];
                if (data.size() > 0) {
                    auto first = data[0];
                    auto id = first["id"].as_int();
                    auto name = first["name"].as_string();
                    auto nested = first["nested"];
                    auto level = nested["level"].as_int();
                    
                    volatile auto v1 = version.size();
                    volatile auto v2 = id;
                    volatile auto v3 = name.size();
                    volatile auto v4 = level;
                    (void)v1; (void)v2; (void)v3; (void)v4;
                }
            } catch (...) {
                // Ignore
            }
        }, ACCESS_ITERATIONS));

        print_results(stats);
        generate_report(stats);
    }

private:
    auto generate_test_json(size_t num_objects) -> std::string {
        std::ostringstream oss;
        oss << "{\n";
        oss << "  \"metadata\": {\n";
        oss << "    \"version\": \"1.0\",\n";
        oss << "    \"count\": " << num_objects << "\n";
        oss << "  },\n";
        oss << "  \"data\": [\n";
        
        for (size_t i = 0; i < num_objects; ++i) {
            if (i > 0) oss << ",\n";
            oss << "    {\n";
            oss << "      \"id\": " << i << ",\n";
            oss << "      \"name\": \"Object " << i << "\",\n";
            oss << "      \"value\": " << (i * 3.14159) << ",\n";
            oss << "      \"active\": " << (i % 2 == 0 ? "true" : "false") << ",\n";
            oss << "      \"nested\": {\n";
            oss << "        \"level\": " << (i % 10) << "\n";
            oss << "      }\n";
            oss << "    }";
        }
        
        oss << "\n  ]\n";
        oss << "}";
        
        return oss.str();
    }

    auto measure_time_ns(std::function<void()> func) -> double {
        auto start = std::chrono::high_resolution_clock::now();
        func();
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(end - start);
        return static_cast<double>(duration.count());
    }

    auto profile_operation(const std::string& name, std::function<void()> operation, int iterations) -> OperationStats {
        std::cout << "Profiling: " << name << " (" << iterations << " iterations)...\n";
        
        OperationStats stats;
        stats.name = name;
        stats.iterations = iterations;
        stats.times_ns.reserve(iterations);

        // Warm up
        for (int i = 0; i < 100; ++i) {
            operation();
        }

        // Actual measurement
        for (int i = 0; i < iterations; ++i) {
            auto time = measure_time_ns(operation);
            stats.times_ns.push_back(time);
        }

        // Calculate statistics
        stats.total_time_ms = std::accumulate(stats.times_ns.begin(), stats.times_ns.end(), 0.0) / 1000000.0;
        stats.avg_time_ns = stats.total_time_ms * 1000000.0 / iterations;
        stats.min_time_ns = *std::min_element(stats.times_ns.begin(), stats.times_ns.end());
        stats.max_time_ns = *std::max_element(stats.times_ns.begin(), stats.times_ns.end());

        // Calculate standard deviation
        double variance = 0.0;
        for (auto time : stats.times_ns) {
            variance += (time - stats.avg_time_ns) * (time - stats.avg_time_ns);
        }
        stats.std_dev_ns = std::sqrt(variance / iterations);

        return stats;
    }

    auto print_results(const std::vector<OperationStats>& stats) -> void {
        std::cout << "\n🔍 Performance Profile Results\n";
        std::cout << "==============================\n\n";

        std::cout << std::fixed << std::setprecision(1);
        std::cout << std::left << std::setw(30) << "Operation"
                  << std::setw(12) << "Avg (ns)"
                  << std::setw(12) << "Min (ns)"
                  << std::setw(12) << "Max (ns)"
                  << std::setw(12) << "StdDev (ns)"
                  << std::setw(12) << "Total (ms)" << "\n";
        std::cout << std::string(90, '-') << "\n";

        for (const auto& stat : stats) {
            std::cout << std::left << std::setw(30) << stat.name
                      << std::setw(12) << stat.avg_time_ns
                      << std::setw(12) << stat.min_time_ns
                      << std::setw(12) << stat.max_time_ns
                      << std::setw(12) << stat.std_dev_ns
                      << std::setw(12) << stat.total_time_ms << "\n";
        }
        std::cout << "\n";

        // Find bottlenecks
        auto slowest = std::max_element(stats.begin(), stats.end(),
            [](const OperationStats& a, const OperationStats& b) {
                return a.avg_time_ns < b.avg_time_ns;
            });

        if (slowest != stats.end()) {
            std::cout << "🐌 Slowest operation: " << slowest->name 
                      << " (" << std::fixed << std::setprecision(1) << slowest->avg_time_ns << " ns/op)\n";
        }

        auto fastest = std::min_element(stats.begin(), stats.end(),
            [](const OperationStats& a, const OperationStats& b) {
                return a.avg_time_ns < b.avg_time_ns;
            });

        if (fastest != stats.end()) {
            std::cout << "⚡ Fastest operation: " << fastest->name 
                      << " (" << std::fixed << std::setprecision(1) << fastest->avg_time_ns << " ns/op)\n";
        }

        if (slowest != stats.end() && fastest != stats.end()) {
            double ratio = slowest->avg_time_ns / fastest->avg_time_ns;
            std::cout << "📊 Performance ratio: " << std::fixed << std::setprecision(1) << ratio << "x\n";
        }
        std::cout << "\n";
    }

    auto generate_report(const std::vector<OperationStats>& stats) -> void {
        std::ofstream report("benchmarks/PROFILE_RESULTS.md");
        if (!report.is_open()) {
            std::cerr << "Failed to create profile report file\n";
            return;
        }

        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);

        report << "# rrjson Performance Profile Results\n\n";
        report << "Generated on: " << std::ctime(&time_t) << "\n";

        report << "## Summary\n\n";
        report << "This report contains detailed performance profiling results for rrjson operations.\n";
        report << "Each operation was measured multiple times to provide statistical analysis.\n\n";

        report << "## Results Table\n\n";
        report << "| Operation | Avg (ns) | Min (ns) | Max (ns) | StdDev (ns) | Total (ms) | Iterations |\n";
        report << "|-----------|----------|----------|----------|-------------|------------|------------|\n";

        for (const auto& stat : stats) {
            report << "| " << stat.name
                   << " | " << std::fixed << std::setprecision(1) << stat.avg_time_ns
                   << " | " << std::fixed << std::setprecision(1) << stat.min_time_ns
                   << " | " << std::fixed << std::setprecision(1) << stat.max_time_ns
                   << " | " << std::fixed << std::setprecision(1) << stat.std_dev_ns
                   << " | " << std::fixed << std::setprecision(3) << stat.total_time_ms
                   << " | " << stat.iterations << " |\n";
        }

        // Analysis section
        report << "\n## Analysis\n\n";

        auto slowest = std::max_element(stats.begin(), stats.end(),
            [](const OperationStats& a, const OperationStats& b) {
                return a.avg_time_ns < b.avg_time_ns;
            });

        auto fastest = std::min_element(stats.begin(), stats.end(),
            [](const OperationStats& a, const OperationStats& b) {
                return a.avg_time_ns < b.avg_time_ns;
            });

        if (slowest != stats.end()) {
            report << "### Performance Bottlenecks\n\n";
            report << "**Slowest Operation**: " << slowest->name
                   << " (" << std::fixed << std::setprecision(1) << slowest->avg_time_ns << " ns/op)\n\n";
        }

        if (fastest != stats.end()) {
            report << "**Fastest Operation**: " << fastest->name
                   << " (" << std::fixed << std::setprecision(1) << fastest->avg_time_ns << " ns/op)\n\n";
        }

        if (slowest != stats.end() && fastest != stats.end()) {
            double ratio = slowest->avg_time_ns / fastest->avg_time_ns;
            report << "**Performance Ratio**: " << std::fixed << std::setprecision(1) << ratio << "x difference\n\n";
        }

        // Categorize operations
        std::vector<const OperationStats*> parsing_ops;
        std::vector<const OperationStats*> access_ops;
        std::vector<const OperationStats*> conversion_ops;

        for (const auto& stat : stats) {
            if (stat.name.find("Parsing") != std::string::npos) {
                parsing_ops.push_back(&stat);
            } else if (stat.name.find("Access") != std::string::npos) {
                access_ops.push_back(&stat);
            } else if (stat.name.find("Conversion") != std::string::npos) {
                conversion_ops.push_back(&stat);
            }
        }

        if (!parsing_ops.empty()) {
            report << "### Parsing Operations\n\n";
            for (const auto* op : parsing_ops) {
                report << "- **" << op->name << "**: "
                       << std::fixed << std::setprecision(1) << op->avg_time_ns << " ns/op\n";
            }
            report << "\n";
        }

        if (!access_ops.empty()) {
            report << "### Access Operations\n\n";
            for (const auto* op : access_ops) {
                report << "- **" << op->name << "**: "
                       << std::fixed << std::setprecision(1) << op->avg_time_ns << " ns/op\n";
            }
            report << "\n";
        }

        if (!conversion_ops.empty()) {
            report << "### Type Conversion Operations\n\n";
            for (const auto* op : conversion_ops) {
                report << "- **" << op->name << "**: "
                       << std::fixed << std::setprecision(1) << op->avg_time_ns << " ns/op\n";
            }
            report << "\n";
        }

        // Recommendations
        report << "## Recommendations\n\n";

        if (slowest != stats.end()) {
            if (slowest->name.find("non-existing") != std::string::npos) {
                report << "1. **Exception Handling Optimization**: The slowest operation involves accessing non-existing keys, "
                       << "suggesting that exception handling is a major bottleneck. Consider adding a `contains()` method.\n\n";
            }

            if (slowest->name.find("Complex Access") != std::string::npos) {
                report << "2. **Access Pattern Optimization**: Complex access patterns are slow, indicating that multiple "
                       << "key lookups and type conversions accumulate overhead.\n\n";
            }
        }

        // Find operations with high variance
        auto high_variance = std::max_element(stats.begin(), stats.end(),
            [](const OperationStats& a, const OperationStats& b) {
                return (a.std_dev_ns / a.avg_time_ns) < (b.std_dev_ns / b.avg_time_ns);
            });

        if (high_variance != stats.end()) {
            double cv = high_variance->std_dev_ns / high_variance->avg_time_ns;
            if (cv > 0.5) {
                report << "3. **Consistency Issues**: " << high_variance->name
                       << " shows high variance (CV: " << std::fixed << std::setprecision(2) << cv
                       << "), indicating inconsistent performance.\n\n";
            }
        }

        report << "## Key Findings\n\n";
        report << "- Exception-based error handling significantly impacts performance\n";
        report << "- Key lookup operations dominate access time\n";
        report << "- Type conversions add measurable overhead\n";
        report << "- Complex access patterns amplify individual operation costs\n\n";

        report << "## Next Steps\n\n";
        report << "1. Implement `contains()` method to avoid exceptions for existence checks\n";
        report << "2. Optimize hash table lookups in Element::operator[]\n";
        report << "3. Consider caching frequently accessed elements\n";
        report << "4. Profile with larger datasets to identify scaling issues\n";

        report.close();
        std::cout << "📊 Detailed report saved to: benchmarks/PROFILE_RESULTS.md\n";
    }
};

int main() {
    std::cout << "🚀 Starting Simple rrjson Performance Profiling...\n\n";

    SimpleProfiler profiler;
    profiler.run_profiling();

    std::cout << "\n✅ Profiling completed!\n\n";
    std::cout << "📁 Report saved to: benchmarks/PROFILE_RESULTS.md\n";

    return 0;
}
