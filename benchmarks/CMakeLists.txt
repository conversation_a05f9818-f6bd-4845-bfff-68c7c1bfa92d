cmake_minimum_required(VERSION 3.16)
project(rrjson_benchmarks)

# Set C++ standard
set(CMAKE_CXX_STANDARD 23)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set build type to Release by default for benchmarks
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Compiler flags for optimization
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -DNDEBUG -march=native")
set(CMAKE_CXX_FLAGS_DEBUG "-g -O0")

# Add warnings
if(CMAKE_CXX_COMPILER_ID MATCHES "GNU|Clang")
    add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# Include directories
include_directories(../lib)

# Find packages
find_package(PkgConfig QUIET)

# Try to find nlohmann/json
find_package(nlohmann_json QUIET)
if(nlohmann_json_FOUND)
    message(STATUS "Found nlohmann/json: ${nlohmann_json_VERSION}")
    set(HAS_NLOHMANN_JSON ON)
else()
    # Try pkg-config
    if(PkgConfig_FOUND)
        pkg_check_modules(NLOHMANN_JSON QUIET nlohmann_json)
        if(NLOHMANN_JSON_FOUND)
            message(STATUS "Found nlohmann/json via pkg-config")
            set(HAS_NLOHMANN_JSON ON)
        endif()
    endif()
    
    # Try to find header directly
    if(NOT HAS_NLOHMANN_JSON)
        find_path(NLOHMANN_JSON_INCLUDE_DIR
            NAMES nlohmann/json.hpp
            PATHS /usr/include /usr/local/include /opt/homebrew/include
        )
        if(NLOHMANN_JSON_INCLUDE_DIR)
            message(STATUS "Found nlohmann/json headers at: ${NLOHMANN_JSON_INCLUDE_DIR}")
            set(HAS_NLOHMANN_JSON ON)
            include_directories(${NLOHMANN_JSON_INCLUDE_DIR})
        endif()
    endif()
endif()

# Try to find RapidJSON
find_path(RAPIDJSON_INCLUDE_DIR
    NAMES rapidjson/rapidjson.h
    PATHS /usr/include /usr/local/include /opt/homebrew/include
)
if(RAPIDJSON_INCLUDE_DIR)
    message(STATUS "Found RapidJSON headers at: ${RAPIDJSON_INCLUDE_DIR}")
    set(HAS_RAPIDJSON ON)
    include_directories(${RAPIDJSON_INCLUDE_DIR})
endif()

# Try to find Boost.JSON
find_package(Boost QUIET COMPONENTS json)
if(Boost_FOUND)
    message(STATUS "Found Boost.JSON: ${Boost_VERSION}")
    set(HAS_BOOST_JSON ON)
endif()

# Try to find simdjson
find_path(SIMDJSON_INCLUDE_DIR
    NAMES simdjson.h
    PATHS /usr/include /usr/local/include /opt/homebrew/include
)
if(SIMDJSON_INCLUDE_DIR)
    find_library(SIMDJSON_LIBRARY
        NAMES simdjson
        PATHS /usr/lib /usr/local/lib /opt/homebrew/lib
    )
    if(SIMDJSON_LIBRARY)
        message(STATUS "Found simdjson: ${SIMDJSON_INCLUDE_DIR}")
        set(HAS_SIMDJSON ON)
        include_directories(${SIMDJSON_INCLUDE_DIR})
    endif()
endif()

# Find gperftools (optional)
find_library(PROFILER_LIB profiler)
find_path(PROFILER_INCLUDE_DIR gperftools/profiler.h)

# Create the main benchmark executable
add_executable(rrjson_vs_nlohmann rrjson_vs_nlohmann.cpp)

# Simple profiler (no external dependencies)
add_executable(simple_profile simple_profile.cpp)

# V2 performance comparison
add_executable(v2_performance_test v2_performance_test.cpp)
target_compile_options(v2_performance_test PRIVATE -mavx2)  # Enable SIMD

# Array performance comparison
add_executable(array_performance_test array_performance_test.cpp)
target_compile_options(array_performance_test PRIVATE -mavx2)  # Enable SIMD

# LazyArray performance test
add_executable(lazy_array_test lazy_array_test.cpp)
target_compile_options(lazy_array_test PRIVATE -mavx2)  # Enable SIMD

# Advanced profiler with gperftools (if available)
if(PROFILER_LIB AND PROFILER_INCLUDE_DIR)
    add_executable(profile_rrjson profile_rrjson.cpp)
    target_include_directories(profile_rrjson PRIVATE ${PROFILER_INCLUDE_DIR})
    target_compile_definitions(profile_rrjson PRIVATE ENABLE_PROFILING)
    target_link_libraries(profile_rrjson ${PROFILER_LIB})
    message(STATUS "Found gperftools - advanced profiling enabled")
    set(HAS_PROFILER ON)
else()
    message(STATUS "gperftools not found - use simple_profile instead")
    set(HAS_PROFILER OFF)
endif()

# Set compile definitions based on found libraries
if(HAS_NLOHMANN_JSON)
    target_compile_definitions(rrjson_vs_nlohmann PRIVATE HAS_NLOHMANN_JSON)
    if(nlohmann_json_FOUND)
        target_link_libraries(rrjson_vs_nlohmann nlohmann_json::nlohmann_json)
    endif()
endif()

if(HAS_RAPIDJSON)
    target_compile_definitions(rrjson_vs_nlohmann PRIVATE HAS_RAPIDJSON)
endif()

if(HAS_BOOST_JSON)
    target_compile_definitions(rrjson_vs_nlohmann PRIVATE HAS_BOOST_JSON)
    target_link_libraries(rrjson_vs_nlohmann ${Boost_LIBRARIES})
endif()

if(HAS_SIMDJSON)
    target_compile_definitions(rrjson_vs_nlohmann PRIVATE HAS_SIMDJSON)
    target_link_libraries(rrjson_vs_nlohmann ${SIMDJSON_LIBRARY})
endif()

# Create additional benchmark targets if they exist
if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/json_benchmark.cpp")
    add_executable(json_benchmark json_benchmark.cpp)
    
    if(HAS_NLOHMANN_JSON)
        target_compile_definitions(json_benchmark PRIVATE HAS_NLOHMANN_JSON)
        if(nlohmann_json_FOUND)
            target_link_libraries(json_benchmark nlohmann_json::nlohmann_json)
        endif()
    endif()
    
    if(HAS_RAPIDJSON)
        target_compile_definitions(json_benchmark PRIVATE HAS_RAPIDJSON)
    endif()
    
    if(HAS_BOOST_JSON)
        target_compile_definitions(json_benchmark PRIVATE HAS_BOOST_JSON)
        target_link_libraries(json_benchmark ${Boost_LIBRARIES})
    endif()
    
    if(HAS_SIMDJSON)
        target_compile_definitions(json_benchmark PRIVATE HAS_SIMDJSON)
        target_link_libraries(json_benchmark ${SIMDJSON_LIBRARY})
    endif()
endif()

if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/json_benchmark_no_simd.cpp")
    add_executable(json_benchmark_no_simd json_benchmark_no_simd.cpp)
    
    if(HAS_NLOHMANN_JSON)
        target_compile_definitions(json_benchmark_no_simd PRIVATE HAS_NLOHMANN_JSON)
        if(nlohmann_json_FOUND)
            target_link_libraries(json_benchmark_no_simd nlohmann_json::nlohmann_json)
        endif()
    endif()
    
    if(HAS_RAPIDJSON)
        target_compile_definitions(json_benchmark_no_simd PRIVATE HAS_RAPIDJSON)
    endif()
    
    if(HAS_BOOST_JSON)
        target_compile_definitions(json_benchmark_no_simd PRIVATE HAS_BOOST_JSON)
        target_link_libraries(json_benchmark_no_simd ${Boost_LIBRARIES})
    endif()
endif()

# Print summary
message(STATUS "")
message(STATUS "Benchmark Configuration Summary:")
message(STATUS "================================")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "")
message(STATUS "Available JSON libraries:")
message(STATUS "  rrjson:         YES (header-only)")
if(HAS_NLOHMANN_JSON)
    message(STATUS "  nlohmann/json:  YES")
else()
    message(STATUS "  nlohmann/json:  NO (install with: sudo apt-get install nlohmann-json3-dev)")
endif()
if(HAS_RAPIDJSON)
    message(STATUS "  RapidJSON:      YES")
else()
    message(STATUS "  RapidJSON:      NO (install with: sudo apt-get install rapidjson-dev)")
endif()
if(HAS_BOOST_JSON)
    message(STATUS "  Boost.JSON:     YES")
else()
    message(STATUS "  Boost.JSON:     NO (install with: sudo apt-get install libboost-json-dev)")
endif()
if(HAS_SIMDJSON)
    message(STATUS "  simdjson:       YES")
else()
    message(STATUS "  simdjson:       NO (install with: sudo apt-get install libsimdjson-dev)")
endif()
message(STATUS "")
message(STATUS "Profiling tools:")
if(HAS_PROFILER)
    message(STATUS "  gperftools:     YES (advanced profiling available)")
else()
    message(STATUS "  gperftools:     NO (install with: sudo apt-get install libgoogle-perftools-dev)")
endif()
message(STATUS "  simple_profile: YES (built-in timing)")
message(STATUS "")
message(STATUS "To install all dependencies, run: ./install_dependencies.sh")
message(STATUS "")
