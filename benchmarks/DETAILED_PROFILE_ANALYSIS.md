# rrjson Detailed Performance Analysis

Generated by analyze_profiles.sh

## Executive Summary

This report provides a comprehensive analysis of rr<PERSON><PERSON>'s performance bottlenecks based on CPU profiling data from gperftools.

## Key Findings

### Critical Performance Issues

1. **Number Parsing Bottleneck**: String-to-number conversion consumes 11.5-18.4% of CPU time
2. **Hash Table Overhead**: Object key lookups consume 6.8-18.4% of CPU time  
3. **Whitespace Processing**: Inefficient character checking consumes 8.5-12.3% of CPU time
4. **Memory Allocation Churn**: Frequent malloc/free calls during parsing

### Performance Comparison: Small vs Medium JSON

## Small JSON Profile (100 objects)
```
Total: 342 samples
      42  12.3%  12.3%       42  12.3% isspace@@GLIBC_2.2.5
      36  10.5%  22.8%       37  10.8% strtof64
      35  10.2%  33.0%       35  10.2% rrjson::Parser::parse_object
      26   7.6%  40.6%       63  18.4% std::__detail::_Map_base::operator[]
      25   7.3%  48.0%       41  12.0% __default_morecore@GLIBC_2.2.5
      24   7.0%  55.0%       24   7.0% rrjson::Parser::parse_string_element
      19   5.6%  60.5%       63  18.4% rrjson::Parser::parse_number_element
      17   5.0%  65.5%       17   5.0% timer_settime@GLIBC_2.2.5
      12   3.5%  69.0%       14   4.1% std::__do_visit [clone .isra.0]
      11   3.2%  72.2%       14   4.1% rrjson::Parser::parse_array
      10   2.9%  75.1%       15   4.4% __libc_free@@GLIBC_2.2.5
      10   2.9%  78.1%       45  13.2% malloc@@GLIBC_2.2.5
       9   2.6%  80.7%        9   2.6% rrjson::Parser::expect_char
       8   2.3%  83.0%       53  15.5% operator new
       8   2.3%  85.4%        8   2.3% std::_Sp_counted_base::_M_release
       6   1.8%  87.1%        6   1.8% __xpg_basename@@GLIBC_2.2.5
       6   1.8%  88.9%       15   4.4% std::_Hashtable::~_Hashtable
       5   1.5%  90.4%        5   1.5% _dl_mcount_wrapper@GLIBC_2.2.5
       5   1.5%  91.8%        5   1.5% rrjson::Element::~Element
```

## Medium JSON Profile (1000 objects)
```
Total: 2997 samples
     344  11.5%  11.5%      346  11.5% strtof64
     333  11.1%  22.6%      333  11.1% rrjson::Parser::parse_object
     310  10.3%  32.9%      310  10.3% rrjson::Parser::parse_string_element
     255   8.5%  41.4%      255   8.5% isspace@@GLIBC_2.2.5
     205   6.8%  48.3%      377  12.6% __default_morecore@GLIBC_2.2.5
     203   6.8%  55.1%      479  16.0% std::__detail::_Map_base::operator[]
     176   5.9%  60.9%      176   5.9% timer_settime@GLIBC_2.2.5
     125   4.2%  65.1%      137   4.6% std::__do_visit [clone .isra.0]
     117   3.9%  69.0%      507  16.9% rrjson::Parser::parse_number_element
     107   3.6%  72.6%      133   4.4% rrjson::Parser::parse_array
      96   3.2%  75.8%       96   3.2% std::_Sp_counted_base::_M_release
      83   2.8%  78.5%       83   2.8% rrjson::Parser::expect_char
      73   2.4%  81.0%      277   9.2% __libc_free@@GLIBC_2.2.5
      62   2.1%  83.0%      325  10.8% std::_Hashtable::~_Hashtable
      58   1.9%  85.0%       89   3.0% rrjson::Parser::parse_value
      50   1.7%  86.7%      196   6.5% malloc@@GLIBC_2.2.5
      48   1.6%  88.3%       49   1.6% std::__detail::_Prime_rehash_policy::_M_need_rehash
      41   1.4%  89.6%       41   1.4% std::__detail::_Prime_rehash_policy::_M_next_bkt
      40   1.3%  91.0%      234   7.8% operator new
```

## Detailed Analysis by Function

### Top Performance Bottlenecks

#### 1. Number Parsing (`strtof64` + `parse_number_element`)
- **Small JSON**: 18.4% of CPU time
- **Medium JSON**: 15.4% of CPU time
- **Issue**: Using libc `strtof64` for every number conversion
- **Impact**: Explains why type conversions take ~670μs each
- **Solution**: Implement fast number parsing without libc

#### 2. Hash Table Operations (`std::__detail::_Map_base::operator[]`)
- **Small JSON**: 18.4% of CPU time  
- **Medium JSON**: 6.8% of CPU time
- **Issue**: std::unordered_map overhead for object key lookups
- **Impact**: Explains why array access takes 680μs per operation
- **Solution**: Custom hash table or optimized key lookup

#### 3. Whitespace Processing (`isspace`)
- **Small JSON**: 12.3% of CPU time
- **Medium JSON**: 8.5% of CPU time  
- **Issue**: Character-by-character whitespace checking
- **Solution**: SIMD-optimized whitespace skipping

#### 4. Object Parsing (`parse_object`)
- **Small JSON**: 10.2% of CPU time
- **Medium JSON**: 11.1% of CPU time
- **Issue**: Complex object parsing logic
- **Solution**: Streamline parsing state machine

### Memory Management Issues

- **malloc/free overhead**: 13.2-6.5% of CPU time
- **Hash table destruction**: 4.4-2.7% of CPU time
- **Frequent allocations during parsing**
- **Solution**: Memory pools and pre-allocation

### Scaling Analysis

Comparing small (100 objects) vs medium (1000 objects) JSON:

1. **Hash table overhead decreases** (18.4% → 6.8%) - good scaling
2. **Number parsing remains high** (18.4% → 15.4%) - consistent bottleneck  
3. **Whitespace processing decreases** (12.3% → 8.5%) - acceptable scaling
4. **Object parsing stable** (10.2% → 11.1%) - good scaling

**Conclusion**: Number parsing is the primary bottleneck regardless of data size.

## Optimization Roadmap

### Phase 1: Critical Fixes (Expected 5-10x improvement)
1. **Fast Number Parsing**: Replace `strtof64` with custom implementation
2. **Hash Table Optimization**: Replace std::unordered_map  
3. **Add contains() method**: Eliminate exception overhead

### Phase 2: Performance Tuning (Expected 2-3x improvement)
4. **SIMD Whitespace Skipping**: Vectorized character processing
5. **Memory Pool**: Reduce allocation overhead
6. **String View Optimization**: Minimize copying

### Phase 3: Advanced Optimizations (Expected 1.5-2x improvement)  
7. **SIMD JSON Parsing**: Vectorized parsing for large data
8. **Branch Prediction Optimization**: Optimize parsing state machine
9. **Cache-Friendly Data Structures**: Improve memory access patterns

## Performance Targets

Based on the profiling data, realistic performance targets are:

- **Number conversion**: 670μs → 50μs (13x improvement)
- **Key lookup**: 680μs → 100μs (6.8x improvement)  
- **Overall parsing**: Current → 10x faster
- **Memory usage**: Reduce allocation overhead by 50%

## Implementation Priority

1. **URGENT**: Number parsing optimization (15.4% CPU time)
2. **HIGH**: Hash table replacement (6.8% CPU time)
3. **MEDIUM**: Whitespace optimization (8.5% CPU time)
4. **LOW**: Memory pool implementation

This analysis provides a data-driven roadmap for optimizing rrjson performance.
