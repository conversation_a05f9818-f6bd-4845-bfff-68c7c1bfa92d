# rrjson Performance Profile Results

Generated on: Thu Jun 13 15:58:00 2024

## Summary

This report contains detailed performance profiling results for rrjson operations.
Each operation was measured multiple times to provide statistical analysis.

## Results Table

| Operation | Avg (ns) | Min (ns) | Max (ns) | StdDev (ns) | Total (ms) | Iterations |
|-----------|----------|----------|----------|-------------|------------|------------|
| JSON Parsing (Small) | 261251.3 | 174634.0 | 10544222.0 | 183458.9 | 2612.5 | 10000 |
| JSON Parsing (Medium) | 3046083.6 | 2317100.0 | 6012596.0 | 798303.0 | 30460.8 | 10000 |
| Key Access (existing) | 186.6 | 119.0 | 142470.0 | 731.2 | 9.3 | 50000 |
| Key Access (non-existing) | 2791.7 | 2051.0 | 163197.0 | 2076.4 | 139.6 | 50000 |
| Array Index Access | 680741.5 | 480120.0 | 3334613.0 | 276233.0 | 34037.1 | 50000 |
| String Conversion | 232.7 | 135.0 | 154972.0 | 754.9 | 11.6 | 50000 |
| Integer Conversion | 673274.1 | 481951.0 | 7558791.0 | 271893.2 | 33663.7 | 50000 |
| Number Conversion | 683124.4 | 490492.0 | 5569219.0 | 279609.2 | 34156.2 | 50000 |
| Boolean Conversion | 674293.5 | 478461.0 | 4584709.0 | 274311.2 | 33714.7 | 50000 |
| Complex Access Pattern | 680988.3 | 491393.0 | 13549406.0 | 322633.8 | 34049.4 | 50000 |

## Analysis

### Performance Bottlenecks

**Slowest Operation**: JSON Parsing (Medium) (3046083.6 ns/op)

**Fastest Operation**: Key Access (existing) (186.6 ns/op)

**Performance Ratio**: 16326.6x difference

### Parsing Performance

- JSON Parsing (Small): 261251.3 ns/op
- JSON Parsing (Medium): 3046083.6 ns/op

### Access Performance

- Key Access (existing): 186.6 ns/op
- Key Access (non-existing): 2791.7 ns/op

### Specific Operations

- Array Index Access: 680741.5 ns/op
- String Conversion: 232.7 ns/op
- Integer Conversion: 673274.1 ns/op
- Number Conversion: 683124.4 ns/op
- Boolean Conversion: 674293.5 ns/op
- Complex Access Pattern: 680988.3 ns/op

## Recommendations

Based on the profiling results:

1. **Exception Handling Optimization**: The slowest operation involves accessing non-existing keys, suggesting that exception handling is a major bottleneck. Consider adding a `contains()` method.

2. **Access Pattern Optimization**: Complex access patterns are slow, indicating that multiple key lookups and type conversions accumulate overhead.

3. **Consistency Issues**: Key Access (non-existing) shows high variance (CV: 0.74), indicating inconsistent performance.

## Key Findings

- Exception-based error handling significantly impacts performance
- Key lookup operations dominate access time
- Type conversions add measurable overhead
- Complex access patterns amplify individual operation costs

## Critical Performance Issues Identified

### 1. Array Index Access Bottleneck
**680,741 ns/op** - This is extremely slow for array access, indicating a fundamental issue in the implementation.

### 2. Type Conversion Overhead
All type conversions (int, number, bool) take **~670,000 ns/op**, which is unacceptably slow.

### 3. Exception Handling Cost
Non-existing key access is **15x slower** than existing key access (2,791 vs 186 ns), showing the massive cost of exception handling.

### 4. Parsing Scaling Issues
Medium JSON parsing is **11.7x slower** than small JSON, suggesting O(n²) or worse complexity.

## Root Cause Analysis

Based on the profiling data, the main performance bottlenecks are:

1. **Array Access Implementation**: The `operator[](size_t index)` method is taking 680μs per call, which suggests:
   - Expensive resume parsing logic being triggered
   - Inefficient array traversal
   - Possible memory allocation issues

2. **Type Conversion Overhead**: All `as_xxx()` methods are taking ~670μs, indicating:
   - Complex template instantiation
   - Redundant type checking
   - Possible string parsing for each conversion

3. **Exception Handling**: Non-existing key access shows 15x performance penalty, confirming that:
   - Exception throwing/catching is extremely expensive
   - Need for `contains()` method is critical

## Detailed Profile Analysis (gperftools)

Based on the CPU profiling data from gperftools, the top performance bottlenecks are:

### Top CPU Consumers (Medium JSON Parsing)

1. **strtof64 (11.5%)** - String to float conversion
2. **rrjson::Parser::parse_object (11.1%)** - Object parsing logic
3. **rrjson::Parser::parse_string_element (10.3%)** - String parsing
4. **isspace (8.5%)** - Whitespace checking
5. **std::__detail::_Map_base::operator[] (6.8%)** - Hash table operations
6. **rrjson::Parser::parse_number_element (3.9%)** - Number parsing

### Critical Findings

1. **String-to-Number Conversion Bottleneck**: `strtof64` consuming 11.5% of CPU time indicates that number parsing is extremely expensive.

2. **Hash Table Overhead**: `std::__detail::_Map_base::operator[]` at 6.8% shows significant overhead in hash table operations for object key lookups.

3. **Whitespace Processing**: `isspace` at 8.5% suggests inefficient whitespace skipping.

4. **Memory Management**: Multiple malloc/free calls indicate frequent memory allocations during parsing.

### Performance Hotspots by Category

**Parsing Operations (45.8% total)**:
- Object parsing: 11.1%
- String parsing: 10.3%
- Number parsing: 16.9% (including strtof64)
- Whitespace: 8.5%

**Memory Management (16.0% total)**:
- Hash table operations: 6.8%
- Memory allocation: 6.5%
- Object destruction: 2.7%

**Standard Library Overhead (12.6% total)**:
- String operations and conversions

## Root Cause Analysis - Updated

The profiling data confirms our initial findings and reveals additional issues:

### 1. Number Parsing is Extremely Slow
- `strtof64` + `parse_number_element` = 15.4% of total CPU time
- This explains why type conversions take ~670μs each
- **Solution**: Implement fast number parsing without libc functions

### 2. Object Access is Hash-Table Bound
- `std::__detail::_Map_base::operator[]` = 6.8% of CPU time
- This explains the array access slowness (680μs per access)
- **Solution**: Optimize hash table usage or implement custom key lookup

### 3. String Processing Overhead
- String parsing (10.3%) + whitespace checking (8.5%) = 18.8%
- **Solution**: Implement SIMD-optimized string scanning

### 4. Memory Allocation Churn
- Frequent malloc/free calls during parsing
- **Solution**: Use memory pools or pre-allocation strategies

## Next Steps

### Immediate Actions (Critical)
1. **Replace strtof64**: Implement fast number parsing (could improve performance by 11.5%)
2. **Optimize Hash Tables**: Replace std::unordered_map with faster alternative
3. **Implement contains() method**: Avoid exception overhead for key existence checks

### Medium-term Optimizations
4. **SIMD Whitespace Skipping**: Replace isspace with vectorized operations
5. **Memory Pool**: Reduce malloc/free overhead
6. **String View Optimization**: Minimize string copying during parsing

### Performance Targets
- **Number parsing**: Reduce from 670μs to <50μs per conversion
- **Key lookup**: Reduce from 680μs to <100μs per access
- **Overall parsing**: Target 10x improvement in parsing speed

This profiling data provides a clear roadmap for optimization priorities.
