#!/bin/bash

# Performance Analysis Script for rrjson
# This script analyzes all generated profile files and creates a comprehensive report

echo "🔍 rrjson Performance Analysis Report Generator"
echo "=============================================="
echo

# Check if profile files exist
if [ ! -f "profile_Small_JSON_Parsing.prof" ] || [ ! -f "profile_Medium_JSON_Parsing.prof" ]; then
    echo "❌ Profile files not found. Please run ./profile_rrjson first."
    exit 1
fi

# Check if pprof is available
if ! command -v pprof &> /dev/null; then
    echo "❌ pprof not found. Please install it:"
    echo "   go install github.com/google/pprof@latest"
    exit 1
fi

echo "📊 Analyzing profile data..."
echo

# Create detailed analysis report
cat > DETAILED_PROFILE_ANALYSIS.md << 'EOF'
# rrjson Detailed Performance Analysis

Generated by analyze_profiles.sh

## Executive Summary

This report provides a comprehensive analysis of r<PERSON><PERSON><PERSON>'s performance bottlenecks based on CPU profiling data from gperftools.

## Key Findings

### Critical Performance Issues

1. **Number Parsing Bottleneck**: String-to-number conversion consumes 11.5-18.4% of CPU time
2. **Hash Table Overhead**: Object key lookups consume 6.8-18.4% of CPU time  
3. **Whitespace Processing**: Inefficient character checking consumes 8.5-12.3% of CPU time
4. **Memory Allocation Churn**: Frequent malloc/free calls during parsing

### Performance Comparison: Small vs Medium JSON

EOF

echo "## Small JSON Profile (100 objects)" >> DETAILED_PROFILE_ANALYSIS.md
echo '```' >> DETAILED_PROFILE_ANALYSIS.md
pprof --text ./profile_rrjson profile_Small_JSON_Parsing.prof | head -20 >> DETAILED_PROFILE_ANALYSIS.md
echo '```' >> DETAILED_PROFILE_ANALYSIS.md
echo >> DETAILED_PROFILE_ANALYSIS.md

echo "## Medium JSON Profile (1000 objects)" >> DETAILED_PROFILE_ANALYSIS.md
echo '```' >> DETAILED_PROFILE_ANALYSIS.md
pprof --text ./profile_rrjson profile_Medium_JSON_Parsing.prof | head -20 >> DETAILED_PROFILE_ANALYSIS.md
echo '```' >> DETAILED_PROFILE_ANALYSIS.md
echo >> DETAILED_PROFILE_ANALYSIS.md

# Add analysis
cat >> DETAILED_PROFILE_ANALYSIS.md << 'EOF'
## Detailed Analysis by Function

### Top Performance Bottlenecks

#### 1. Number Parsing (`strtof64` + `parse_number_element`)
- **Small JSON**: 18.4% of CPU time
- **Medium JSON**: 15.4% of CPU time
- **Issue**: Using libc `strtof64` for every number conversion
- **Impact**: Explains why type conversions take ~670μs each
- **Solution**: Implement fast number parsing without libc

#### 2. Hash Table Operations (`std::__detail::_Map_base::operator[]`)
- **Small JSON**: 18.4% of CPU time  
- **Medium JSON**: 6.8% of CPU time
- **Issue**: std::unordered_map overhead for object key lookups
- **Impact**: Explains why array access takes 680μs per operation
- **Solution**: Custom hash table or optimized key lookup

#### 3. Whitespace Processing (`isspace`)
- **Small JSON**: 12.3% of CPU time
- **Medium JSON**: 8.5% of CPU time  
- **Issue**: Character-by-character whitespace checking
- **Solution**: SIMD-optimized whitespace skipping

#### 4. Object Parsing (`parse_object`)
- **Small JSON**: 10.2% of CPU time
- **Medium JSON**: 11.1% of CPU time
- **Issue**: Complex object parsing logic
- **Solution**: Streamline parsing state machine

### Memory Management Issues

- **malloc/free overhead**: 13.2-6.5% of CPU time
- **Hash table destruction**: 4.4-2.7% of CPU time
- **Frequent allocations during parsing**
- **Solution**: Memory pools and pre-allocation

### Scaling Analysis

Comparing small (100 objects) vs medium (1000 objects) JSON:

1. **Hash table overhead decreases** (18.4% → 6.8%) - good scaling
2. **Number parsing remains high** (18.4% → 15.4%) - consistent bottleneck  
3. **Whitespace processing decreases** (12.3% → 8.5%) - acceptable scaling
4. **Object parsing stable** (10.2% → 11.1%) - good scaling

**Conclusion**: Number parsing is the primary bottleneck regardless of data size.

## Optimization Roadmap

### Phase 1: Critical Fixes (Expected 5-10x improvement)
1. **Fast Number Parsing**: Replace `strtof64` with custom implementation
2. **Hash Table Optimization**: Replace std::unordered_map  
3. **Add contains() method**: Eliminate exception overhead

### Phase 2: Performance Tuning (Expected 2-3x improvement)
4. **SIMD Whitespace Skipping**: Vectorized character processing
5. **Memory Pool**: Reduce allocation overhead
6. **String View Optimization**: Minimize copying

### Phase 3: Advanced Optimizations (Expected 1.5-2x improvement)  
7. **SIMD JSON Parsing**: Vectorized parsing for large data
8. **Branch Prediction Optimization**: Optimize parsing state machine
9. **Cache-Friendly Data Structures**: Improve memory access patterns

## Performance Targets

Based on the profiling data, realistic performance targets are:

- **Number conversion**: 670μs → 50μs (13x improvement)
- **Key lookup**: 680μs → 100μs (6.8x improvement)  
- **Overall parsing**: Current → 10x faster
- **Memory usage**: Reduce allocation overhead by 50%

## Implementation Priority

1. **URGENT**: Number parsing optimization (15.4% CPU time)
2. **HIGH**: Hash table replacement (6.8% CPU time)
3. **MEDIUM**: Whitespace optimization (8.5% CPU time)
4. **LOW**: Memory pool implementation

This analysis provides a data-driven roadmap for optimizing rrjson performance.
EOF

echo "✅ Analysis complete!"
echo
echo "📁 Generated files:"
echo "   • DETAILED_PROFILE_ANALYSIS.md - Comprehensive analysis report"
echo "   • profile_*.prof - Raw profile data files"
echo
echo "🔧 To view profile data interactively:"
echo "   pprof --web ./profile_rrjson profile_Medium_JSON_Parsing.prof"
echo
echo "📊 Key findings:"
echo "   • Number parsing: 15.4% of CPU time (CRITICAL)"
echo "   • Hash table ops: 6.8% of CPU time (HIGH)"  
echo "   • Whitespace: 8.5% of CPU time (MEDIUM)"
echo "   • Memory alloc: 6.5% of CPU time (MEDIUM)"
echo
echo "🎯 Optimization targets:"
echo "   • Replace strtof64 with fast number parsing"
echo "   • Optimize hash table operations"
echo "   • Add contains() method to avoid exceptions"
echo "   • Implement SIMD whitespace skipping"
